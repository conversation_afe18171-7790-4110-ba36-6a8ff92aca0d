"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
class MemoryManager {
    constructor() {
        this.memoryStats = [];
        this.MAX_MEMORY_STATS = 100; // Keep last 100 memory readings
        this.MEMORY_CHECK_INTERVAL = 5 * 60 * 1000; // Check every 5 minutes
        this.MAX_HEAP_USAGE_MB = 80; // Alert if heap usage exceeds 80MB
        this.startMemoryMonitoring();
    }
    static getInstance() {
        if (!MemoryManager.instance) {
            MemoryManager.instance = new MemoryManager();
        }
        return MemoryManager.instance;
    }
    /**
     * Start periodic memory monitoring
     */
    startMemoryMonitoring() {
        this.memoryCheckTimer = setInterval(() => {
            this.recordMemoryStats();
            this.checkMemoryUsage();
        }, this.MEMORY_CHECK_INTERVAL);
        console.log('[Memory Manager] Started memory monitoring');
    }
    /**
     * Stop memory monitoring
     */
    stopMemoryMonitoring() {
        if (this.memoryCheckTimer) {
            clearInterval(this.memoryCheckTimer);
            this.memoryCheckTimer = undefined;
        }
        console.log('[Memory Manager] Stopped memory monitoring');
    }
    /**
     * Record current memory statistics
     */
    recordMemoryStats() {
        const memUsage = process.memoryUsage();
        const stats = {
            heapUsed: memUsage.heapUsed,
            heapTotal: memUsage.heapTotal,
            external: memUsage.external,
            rss: memUsage.rss,
            timestamp: Date.now()
        };
        this.memoryStats.push(stats);
        // Keep only the last MAX_MEMORY_STATS readings
        if (this.memoryStats.length > this.MAX_MEMORY_STATS) {
            this.memoryStats = this.memoryStats.slice(-this.MAX_MEMORY_STATS);
        }
    }
    /**
     * Check if memory usage is concerning
     */
    checkMemoryUsage() {
        const current = process.memoryUsage();
        const heapUsedMB = current.heapUsed / 1024 / 1024;
        const rssMB = current.rss / 1024 / 1024;
        if (heapUsedMB > this.MAX_HEAP_USAGE_MB) {
            console.warn(`[Memory Manager] High heap usage detected: ${heapUsedMB.toFixed(2)}MB`);
            this.performEmergencyCleanup();
        }
        // Log memory stats every hour
        if (this.memoryStats.length > 0 && this.memoryStats.length % 12 === 0) {
            console.log(`[Memory Manager] Memory stats - Heap: ${heapUsedMB.toFixed(2)}MB, RSS: ${rssMB.toFixed(2)}MB`);
        }
    }
    /**
     * Perform emergency cleanup when memory usage is high
     */
    performEmergencyCleanup() {
        console.log('[Memory Manager] Performing emergency cleanup');
        // Force garbage collection if available
        if (global.gc) {
            global.gc();
        }
        console.log('[Memory Manager] Emergency cleanup completed');
    }
    /**
     * Get current memory statistics
     */
    getMemoryStats() {
        const current = process.memoryUsage();
        const currentStats = {
            heapUsed: current.heapUsed,
            heapTotal: current.heapTotal,
            external: current.external,
            rss: current.rss,
            timestamp: Date.now()
        };
        // Calculate averages from recent stats
        const recentStats = this.memoryStats.slice(-10); // Last 10 readings
        const average = {};
        if (recentStats.length > 0) {
            average.heapUsed = recentStats.reduce((sum, stat) => sum + stat.heapUsed, 0) / recentStats.length;
            average.heapTotal = recentStats.reduce((sum, stat) => sum + stat.heapTotal, 0) / recentStats.length;
            average.rss = recentStats.reduce((sum, stat) => sum + stat.rss, 0) / recentStats.length;
        }
        return {
            current: currentStats,
            average
        };
    }
}
exports.default = MemoryManager;
