import { C<PERSON>, Guild, GuildMember } from 'discord.js';
import MilestoneConfiguration from '../models/MilestoneConfiguration';
import UserActivity from '../models/UserActivity';
import MilestoneAchievement from '../models/MilestoneAchievement';
import MilestoneRateLimit from '../models/MilestoneRateLimit';
import { adjustBalance } from './economyService';
import { DatabaseError, logDatabaseOperation } from '../utils/errorHandler';
import { MilestoneAuditService } from './milestoneAuditService';

// Configuration constants
const MAX_WEEKLY_MILESTONE_REWARDS = 50;
const MAX_DAILY_MILESTONE_REWARDS = 10;
const SUSPICIOUS_ACTIVITY_THRESHOLD = 5;
const BLACKLIST_DURATION_HOURS = 24;

// Anti-exploitation constants
const MIN_MESSAGE_LENGTH = 10;
const MIN_VOICE_SESSION_MINUTES = 5;
const MAX_ACHIEVEMENTS_PER_HOUR = 3;
const SPAM_DETECTION_WINDOW_MINUTES = 5;
const DUPLICATE_MESSAGE_THRESHOLD = 3;
const RAPID_ACTIVITY_THRESHOLD = 10; // Actions per minute

export interface MilestoneCheckResult {
    achieved: boolean;
    milestoneType: string;
    category: string;
    rewardAmount: number;
    details: string;
    achievementValue: number;
    diminishingFactor: number;
    achievementCount: number;
}

/**
 * Main function to check and process milestones for a user
 */
export async function checkAndProcessMilestones(
    client: Client,
    discordId: string,
    guildId: string,
    activityType: 'message' | 'voice' | 'reaction' | 'login',
    activityData?: any
): Promise<MilestoneCheckResult[]> {
    try {
        logDatabaseOperation('Checking milestones', { discordId, guildId, activityType });

        // Get user activity data
        const userActivity = await ensureUserActivity(discordId, guildId);
        
        // Update activity based on type
        await updateUserActivity(userActivity, activityType, activityData);

        // Get enabled milestone configurations for this guild
        const milestoneConfigs = await MilestoneConfiguration.find({
            guildId,
            enabled: true
        });

        if (milestoneConfigs.length === 0) {
            return [];
        }

        const achievements: MilestoneCheckResult[] = [];

        // Check each milestone type
        for (const config of milestoneConfigs) {
            const result = await checkSpecificMilestone(
                client,
                discordId,
                guildId,
                userActivity,
                config
            );

            if (result && result.achieved) {
                achievements.push(result);
            }
        }

        return achievements;

    } catch (error) {
        console.error('[Milestone Service] Error checking milestones:', error);
        throw new DatabaseError('Failed to check milestones');
    }
}

/**
 * Ensures user activity record exists
 */
async function ensureUserActivity(discordId: string, guildId: string) {
    let userActivity = await UserActivity.findOne({ discordId, guildId });
    
    if (!userActivity) {
        userActivity = await UserActivity.create({
            discordId,
            guildId,
            serverJoinDate: new Date(),
            lastSeen: new Date(),
            lastLoginDate: new Date(),
            lastDailyReset: new Date(),
            lastWeeklyReset: new Date()
        });
    }

    return userActivity;
}

/**
 * Updates user activity based on activity type
 */
async function updateUserActivity(
    userActivity: any,
    activityType: 'message' | 'voice' | 'reaction' | 'login',
    activityData?: any
): Promise<void> {
    const now = new Date();
    
    // Check if we need to reset daily/weekly counters
    await resetCountersIfNeeded(userActivity, now);

    // Update last seen
    userActivity.lastSeen = now;

    switch (activityType) {
        case 'message':
            await updateMessageActivity(userActivity, activityData, now);
            break;
        case 'voice':
            await updateVoiceActivity(userActivity, activityData, now);
            break;
        case 'reaction':
            await updateReactionActivity(userActivity, activityData, now);
            break;
        case 'login':
            await updateLoginActivity(userActivity, now);
            break;
    }

    await userActivity.save();
}

/**
 * Resets daily/weekly counters if needed
 */
async function resetCountersIfNeeded(userActivity: any, now: Date): Promise<void> {
    const lastDailyReset = new Date(userActivity.lastDailyReset);
    const lastWeeklyReset = new Date(userActivity.lastWeeklyReset);
    
    // Check if we need daily reset (different day)
    if (now.getDate() !== lastDailyReset.getDate() || 
        now.getMonth() !== lastDailyReset.getMonth() || 
        now.getFullYear() !== lastDailyReset.getFullYear()) {
        
        userActivity.dailyMessageCount = 0;
        userActivity.dailyVoiceMinutes = 0;
        userActivity.dailyReactionCount = 0;
        userActivity.uniqueChannelsToday = [];
        userActivity.uniqueVoiceChannelsToday = [];
        userActivity.uniqueReactionTypesToday = [];
        userActivity.lastDailyReset = now;
    }

    // Check if we need weekly reset (different week)
    const currentWeek = getWeekNumber(now);
    const lastResetWeek = getWeekNumber(lastWeeklyReset);
    
    if (currentWeek !== lastResetWeek || now.getFullYear() !== lastWeeklyReset.getFullYear()) {
        userActivity.weeklyMessageCount = 0;
        userActivity.weeklyVoiceMinutes = 0;
        userActivity.weeklyReactionCount = 0;
        userActivity.uniqueChannelsThisWeek = [];
        userActivity.uniqueVoiceChannelsThisWeek = [];
        userActivity.uniqueReactionTypesThisWeek = [];
        userActivity.lastWeeklyReset = now;
    }
}

/**
 * Updates message activity with quality validation
 */
async function updateMessageActivity(userActivity: any, activityData: any, now: Date): Promise<void> {
    // Validate message quality if content is provided
    if (activityData?.messageContent && activityData?.channelId) {
        const qualityCheck = await validateMessageQuality(
            userActivity.discordId,
            userActivity.guildId,
            activityData.messageContent,
            activityData.channelId
        );

        if (!qualityCheck.valid) {
            console.log(`[Milestone Service] Message quality check failed for ${userActivity.discordId}: ${qualityCheck.reason}`);
            return; // Don't update activity for low-quality messages
        }
    }

    userActivity.dailyMessageCount += 1;
    userActivity.weeklyMessageCount += 1;
    userActivity.totalMessages += 1;
    userActivity.lastMessageDate = now;

    if (activityData?.channelId) {
        if (!userActivity.uniqueChannelsToday.includes(activityData.channelId)) {
            userActivity.uniqueChannelsToday.push(activityData.channelId);
        }
        if (!userActivity.uniqueChannelsThisWeek.includes(activityData.channelId)) {
            userActivity.uniqueChannelsThisWeek.push(activityData.channelId);
        }
    }
}

/**
 * Updates voice activity
 */
async function updateVoiceActivity(userActivity: any, activityData: any, now: Date): Promise<void> {
    const minutes = activityData?.minutes || 1;
    
    userActivity.dailyVoiceMinutes += minutes;
    userActivity.weeklyVoiceMinutes += minutes;
    userActivity.totalVoiceMinutes += minutes;
    userActivity.lastVoiceDate = now;

    if (activityData?.channelId) {
        if (!userActivity.uniqueVoiceChannelsToday.includes(activityData.channelId)) {
            userActivity.uniqueVoiceChannelsToday.push(activityData.channelId);
        }
        if (!userActivity.uniqueVoiceChannelsThisWeek.includes(activityData.channelId)) {
            userActivity.uniqueVoiceChannelsThisWeek.push(activityData.channelId);
        }
    }
}

/**
 * Updates reaction activity
 */
async function updateReactionActivity(userActivity: any, activityData: any, now: Date): Promise<void> {
    userActivity.dailyReactionCount += 1;
    userActivity.weeklyReactionCount += 1;
    userActivity.totalReactions += 1;
    userActivity.lastReactionDate = now;

    if (activityData?.emoji) {
        const emojiId = activityData.emoji.id || activityData.emoji.name;
        if (!userActivity.uniqueReactionTypesToday.includes(emojiId)) {
            userActivity.uniqueReactionTypesToday.push(emojiId);
        }
        if (!userActivity.uniqueReactionTypesThisWeek.includes(emojiId)) {
            userActivity.uniqueReactionTypesThisWeek.push(emojiId);
        }
    }
}

/**
 * Updates login activity and streak
 */
async function updateLoginActivity(userActivity: any, now: Date): Promise<void> {
    const lastLogin = new Date(userActivity.lastLoginDate);
    const daysDiff = Math.floor((now.getTime() - lastLogin.getTime()) / (1000 * 60 * 60 * 24));

    if (daysDiff === 1) {
        // Consecutive day - increment streak
        userActivity.loginStreak += 1;
        userActivity.totalDaysActive += 1;
    } else if (daysDiff === 0) {
        // Same day - no change to streak
        return;
    } else {
        // Streak broken - reset to 1
        userActivity.loginStreak = 1;
        userActivity.totalDaysActive += 1;
    }

    // Update longest streak if current is longer
    if (userActivity.loginStreak > userActivity.longestLoginStreak) {
        userActivity.longestLoginStreak = userActivity.loginStreak;
    }

    userActivity.lastLoginDate = now;
}

/**
 * Gets week number of year
 */
function getWeekNumber(date: Date): number {
    const d = new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate()));
    const dayNum = d.getUTCDay() || 7;
    d.setUTCDate(d.getUTCDate() + 4 - dayNum);
    const yearStart = new Date(Date.UTC(d.getUTCFullYear(), 0, 1));
    return Math.ceil((((d.getTime() - yearStart.getTime()) / 86400000) + 1) / 7);
}

/**
 * Gets day of year
 */
function getDayOfYear(date: Date): number {
    const start = new Date(date.getFullYear(), 0, 0);
    const diff = date.getTime() - start.getTime();
    return Math.floor(diff / (1000 * 60 * 60 * 24));
}

/**
 * Checks a specific milestone for achievement
 */
async function checkSpecificMilestone(
    client: Client,
    discordId: string,
    guildId: string,
    userActivity: any,
    config: any
): Promise<MilestoneCheckResult | null> {
    try {
        // Check user security first
        const securityCheck = await checkUserSecurity(discordId, guildId, config.milestoneType);
        if (!securityCheck.allowed) {
            return null;
        }

        // Check rate limiting
        const rateLimitCheck = await checkRateLimit(discordId, guildId, config.milestoneType, config);
        if (!rateLimitCheck.allowed) {
            return null;
        }

        // Check if milestone criteria is met
        const achievementValue = await getMilestoneAchievementValue(userActivity, config);
        if (achievementValue < (config.requirements.threshold || 1)) {
            return null;
        }

        // Check cooldown
        if (await isOnCooldown(discordId, guildId, config.milestoneType, config.requirements.cooldownHours || 24)) {
            return null;
        }

        // Calculate reward with diminishing returns
        const achievementCount = await getAchievementCount(discordId, config.milestoneType);
        const diminishingFactor = config.diminishingReturns ?
            Math.pow(config.diminishingFactor, achievementCount) : 1.0;
        const rewardAmount = Math.max(1, Math.floor(config.rewardAmount * diminishingFactor));

        // Process the achievement
        await processAchievement(
            client,
            discordId,
            guildId,
            config,
            achievementValue,
            rewardAmount,
            diminishingFactor,
            achievementCount + 1
        );

        return {
            achieved: true,
            milestoneType: config.milestoneType,
            category: config.category,
            rewardAmount,
            details: generateAchievementDetails(config.milestoneType, achievementValue),
            achievementValue,
            diminishingFactor,
            achievementCount: achievementCount + 1
        };

    } catch (error) {
        console.error(`[Milestone Service] Error checking milestone ${config.milestoneType}:`, error);
        return null;
    }
}

/**
 * Checks rate limiting for milestone achievements
 */
async function checkRateLimit(
    discordId: string,
    guildId: string,
    milestoneType: string,
    config: any
): Promise<{ allowed: boolean; reason?: string }> {
    const now = new Date();
    const currentWeek = getWeekNumber(now);
    const currentDay = getDayOfYear(now);
    const currentYear = now.getFullYear();

    // Check weekly cap across all milestones
    const weeklyCount = await MilestoneAchievement.countDocuments({
        discordId,
        year: currentYear,
        weekNumber: currentWeek
    });

    if (weeklyCount >= MAX_WEEKLY_MILESTONE_REWARDS) {
        await MilestoneAuditService.logRateLimitHit(
            guildId,
            discordId,
            'weekly_global_limit',
            `Weekly milestone limit reached: ${weeklyCount}/${MAX_WEEKLY_MILESTONE_REWARDS}`
        );
        return { allowed: false, reason: 'Weekly milestone limit reached' };
    }

    // Check daily cap across all milestones
    const dailyCount = await MilestoneAchievement.countDocuments({
        discordId,
        year: currentYear,
        dayOfYear: currentDay
    });

    if (dailyCount >= MAX_DAILY_MILESTONE_REWARDS) {
        await MilestoneAuditService.logRateLimitHit(
            guildId,
            discordId,
            'daily_global_limit',
            `Daily milestone limit reached: ${dailyCount}/${MAX_DAILY_MILESTONE_REWARDS}`
        );
        return { allowed: false, reason: 'Daily milestone limit reached' };
    }

    // Check specific milestone type limits
    const rateLimit = await MilestoneRateLimit.findOne({
        discordId,
        guildId,
        milestoneType
    });

    if (rateLimit) {
        // Check if blacklisted
        if (rateLimit.isBlacklisted && rateLimit.blacklistUntil && rateLimit.blacklistUntil > now) {
            return { allowed: false, reason: 'User is temporarily blacklisted' };
        }

        // Reset daily/weekly counters if needed
        await resetRateLimitCounters(rateLimit, now);

        // Check daily limit for this milestone type
        if (rateLimit.dailyCount >= config.maxRewardsPerDay) {
            await MilestoneAuditService.logRateLimitHit(
                guildId,
                discordId,
                'daily_milestone_limit',
                `Daily limit reached for ${milestoneType}: ${rateLimit.dailyCount}/${config.maxRewardsPerDay}`
            );
            return { allowed: false, reason: 'Daily limit reached for this milestone type' };
        }

        // Check weekly limit for this milestone type
        if (rateLimit.weeklyCount >= config.maxRewardsPerWeek) {
            await MilestoneAuditService.logRateLimitHit(
                guildId,
                discordId,
                'weekly_milestone_limit',
                `Weekly limit reached for ${milestoneType}: ${rateLimit.weeklyCount}/${config.maxRewardsPerWeek}`
            );
            return { allowed: false, reason: 'Weekly limit reached for this milestone type' };
        }
    }

    return { allowed: true };
}

/**
 * Resets rate limit counters if needed
 */
async function resetRateLimitCounters(rateLimit: any, now: Date): Promise<void> {
    const lastDailyReset = new Date(rateLimit.lastDailyReset);
    const lastWeeklyReset = new Date(rateLimit.lastWeeklyReset);

    let needsSave = false;

    // Check daily reset
    if (now.getDate() !== lastDailyReset.getDate() ||
        now.getMonth() !== lastDailyReset.getMonth() ||
        now.getFullYear() !== lastDailyReset.getFullYear()) {
        rateLimit.dailyCount = 0;
        rateLimit.lastDailyReset = now;
        needsSave = true;
    }

    // Check weekly reset
    const currentWeek = getWeekNumber(now);
    const lastResetWeek = getWeekNumber(lastWeeklyReset);

    if (currentWeek !== lastResetWeek || now.getFullYear() !== lastWeeklyReset.getFullYear()) {
        rateLimit.weeklyCount = 0;
        rateLimit.lastWeeklyReset = now;
        needsSave = true;
    }

    if (needsSave) {
        await rateLimit.save();
    }
}

/**
 * Gets the achievement value for a specific milestone type
 */
async function getMilestoneAchievementValue(userActivity: any, config: any): Promise<number> {
    switch (config.milestoneType) {
        case 'login_streak':
            return userActivity.loginStreak;

        case 'channel_diversity_daily':
            return userActivity.uniqueChannelsToday.length;

        case 'channel_diversity_weekly':
            return userActivity.uniqueChannelsThisWeek.length;

        case 'voice_diversity_daily':
            return userActivity.uniqueVoiceChannelsToday.length;

        case 'voice_diversity_weekly':
            return userActivity.uniqueVoiceChannelsThisWeek.length;

        case 'reaction_diversity_daily':
            return userActivity.uniqueReactionTypesToday.length;

        case 'reaction_diversity_weekly':
            return userActivity.uniqueReactionTypesThisWeek.length;

        case 'server_anniversary':
            const daysSinceJoin = Math.floor((Date.now() - userActivity.serverJoinDate.getTime()) / (1000 * 60 * 60 * 24));
            return Math.floor(daysSinceJoin / 30); // Monthly anniversaries

        case 'total_activity_milestone':
            return userActivity.totalDaysActive;

        case 'voice_time_daily':
            return userActivity.dailyVoiceMinutes;

        case 'voice_time_weekly':
            return userActivity.weeklyVoiceMinutes;

        case 'message_count_daily':
            return userActivity.dailyMessageCount;

        case 'message_count_weekly':
            return userActivity.weeklyMessageCount;

        default:
            return 0;
    }
}

/**
 * Checks if user is on cooldown for a milestone type
 */
async function isOnCooldown(
    discordId: string,
    guildId: string,
    milestoneType: string,
    cooldownHours: number
): Promise<boolean> {
    const rateLimit = await MilestoneRateLimit.findOne({
        discordId,
        guildId,
        milestoneType
    });

    if (!rateLimit) {
        return false;
    }

    const now = new Date();
    return rateLimit.cooldownUntil > now;
}

/**
 * Gets the number of times a user has achieved a specific milestone
 */
async function getAchievementCount(discordId: string, milestoneType: string): Promise<number> {
    return await MilestoneAchievement.countDocuments({
        discordId,
        milestoneType
    });
}

/**
 * Processes a milestone achievement
 */
async function processAchievement(
    client: Client,
    discordId: string,
    guildId: string,
    config: any,
    achievementValue: number,
    rewardAmount: number,
    diminishingFactor: number,
    achievementCount: number
): Promise<void> {
    const now = new Date();
    const weekNumber = getWeekNumber(now);
    const dayOfYear = getDayOfYear(now);
    const year = now.getFullYear();

    // Create achievement record
    await MilestoneAchievement.create({
        discordId,
        guildId,
        milestoneType: config.milestoneType,
        category: config.category,
        achievementValue,
        rewardAmount,
        diminishingFactor,
        achievementCount,
        details: generateAchievementDetails(config.milestoneType, achievementValue),
        timestamp: now,
        weekNumber,
        dayOfYear,
        year
    });

    // Update rate limiting
    await updateRateLimit(discordId, guildId, config.milestoneType, config.requirements.cooldownHours || 24);

    // Award the coins
    await adjustBalance(
        discordId,
        rewardAmount,
        'milestone',
        generateAchievementDetails(config.milestoneType, achievementValue),
        client,
        guildId
    );

    // Log the achievement
    await MilestoneAuditService.logMilestoneAchievement(
        guildId,
        discordId,
        config.milestoneType,
        rewardAmount,
        generateAchievementDetails(config.milestoneType, achievementValue),
        client
    );

    console.log(`[Milestone Service] User ${discordId} achieved ${config.milestoneType} milestone: ${rewardAmount} PLC`);
}

/**
 * Updates rate limiting for a user
 */
async function updateRateLimit(
    discordId: string,
    guildId: string,
    milestoneType: string,
    cooldownHours: number
): Promise<void> {
    const now = new Date();
    const cooldownUntil = new Date(now.getTime() + (cooldownHours * 60 * 60 * 1000));

    await MilestoneRateLimit.findOneAndUpdate(
        { discordId, guildId, milestoneType },
        {
            $inc: { dailyCount: 1, weeklyCount: 1 },
            $set: {
                lastAchievement: now,
                cooldownUntil
            },
            $setOnInsert: {
                discordId,
                guildId,
                milestoneType,
                suspiciousActivityCount: 0,
                isBlacklisted: false,
                lastDailyReset: now,
                lastWeeklyReset: now
            }
        },
        { upsert: true, new: true }
    );
}

/**
 * Generates human-readable achievement details
 */
function generateAchievementDetails(milestoneType: string, achievementValue: number): string {
    switch (milestoneType) {
        case 'login_streak':
            return `${achievementValue} day login streak milestone`;

        case 'channel_diversity_daily':
            return `Active in ${achievementValue} different channels today`;

        case 'channel_diversity_weekly':
            return `Active in ${achievementValue} different channels this week`;

        case 'voice_diversity_daily':
            return `Joined ${achievementValue} different voice channels today`;

        case 'voice_diversity_weekly':
            return `Joined ${achievementValue} different voice channels this week`;

        case 'reaction_diversity_daily':
            return `Used ${achievementValue} different reaction types today`;

        case 'reaction_diversity_weekly':
            return `Used ${achievementValue} different reaction types this week`;

        case 'server_anniversary':
            return `${achievementValue} month server anniversary milestone`;

        case 'total_activity_milestone':
            return `${achievementValue} total active days milestone`;

        case 'voice_time_daily':
            return `${achievementValue} minutes in voice chat today`;

        case 'voice_time_weekly':
            return `${achievementValue} minutes in voice chat this week`;

        case 'message_count_daily':
            return `${achievementValue} messages sent today`;

        case 'message_count_weekly':
            return `${achievementValue} messages sent this week`;

        default:
            return `${milestoneType} milestone achieved`;
    }
}

/**
 * Gets user milestone statistics
 */
export async function getUserMilestoneStats(discordId: string, guildId: string): Promise<{
    totalAchievements: number;
    totalRewards: number;
    recentAchievements: any[];
    currentStreaks: any;
    weeklyProgress: any;
}> {
    try {
        const [achievements, userActivity] = await Promise.all([
            MilestoneAchievement.find({ discordId, guildId })
                .sort({ timestamp: -1 })
                .limit(10),
            UserActivity.findOne({ discordId, guildId })
        ]);

        const totalAchievements = await MilestoneAchievement.countDocuments({ discordId, guildId });
        const totalRewards = await MilestoneAchievement.aggregate([
            { $match: { discordId, guildId } },
            { $group: { _id: null, total: { $sum: '$rewardAmount' } } }
        ]);

        const currentWeek = getWeekNumber(new Date());
        const currentYear = new Date().getFullYear();
        const weeklyProgress = await MilestoneAchievement.countDocuments({
            discordId,
            guildId,
            year: currentYear,
            weekNumber: currentWeek
        });

        return {
            totalAchievements,
            totalRewards: totalRewards[0]?.total || 0,
            recentAchievements: achievements,
            currentStreaks: {
                loginStreak: userActivity?.loginStreak || 0,
                longestLoginStreak: userActivity?.longestLoginStreak || 0
            },
            weeklyProgress: {
                achievementsThisWeek: weeklyProgress,
                maxWeeklyAchievements: MAX_WEEKLY_MILESTONE_REWARDS
            }
        };
    } catch (error) {
        console.error('[Milestone Service] Error getting user stats:', error);
        throw new DatabaseError('Failed to get milestone statistics');
    }
}

/**
 * Gets milestone leaderboard for a guild
 */
export async function getMilestoneLeaderboard(guildId: string, limit: number = 10): Promise<any[]> {
    try {
        return await MilestoneAchievement.aggregate([
            { $match: { guildId } },
            {
                $group: {
                    _id: '$discordId',
                    totalAchievements: { $sum: 1 },
                    totalRewards: { $sum: '$rewardAmount' },
                    lastAchievement: { $max: '$timestamp' }
                }
            },
            { $sort: { totalRewards: -1, totalAchievements: -1 } },
            { $limit: limit }
        ]);
    } catch (error) {
        console.error('[Milestone Service] Error getting leaderboard:', error);
        throw new DatabaseError('Failed to get milestone leaderboard');
    }
}

/**
 * Creates default milestone configurations for a guild
 */
export async function createDefaultMilestoneConfigurations(guildId: string): Promise<void> {
    const defaultConfigs = [
        // Time-based milestones
        {
            guildId,
            category: 'time_based',
            milestoneType: 'login_streak',
            enabled: true,
            rewardAmount: 10,
            maxRewardsPerWeek: 7,
            maxRewardsPerDay: 1,
            diminishingReturns: true,
            diminishingFactor: 0.9,
            requirements: { threshold: 3, cooldownHours: 24 }
        },
        {
            guildId,
            category: 'time_based',
            milestoneType: 'server_anniversary',
            enabled: true,
            rewardAmount: 50,
            maxRewardsPerWeek: 1,
            maxRewardsPerDay: 1,
            diminishingReturns: false,
            diminishingFactor: 1.0,
            requirements: { threshold: 1, cooldownHours: 720 } // 30 days
        },

        // Participation diversity milestones
        {
            guildId,
            category: 'participation_diversity',
            milestoneType: 'channel_diversity_daily',
            enabled: true,
            rewardAmount: 5,
            maxRewardsPerWeek: 7,
            maxRewardsPerDay: 1,
            diminishingReturns: true,
            diminishingFactor: 0.8,
            requirements: { threshold: 5, cooldownHours: 24 }
        },
        {
            guildId,
            category: 'participation_diversity',
            milestoneType: 'reaction_diversity_weekly',
            enabled: true,
            rewardAmount: 15,
            maxRewardsPerWeek: 1,
            maxRewardsPerDay: 1,
            diminishingReturns: true,
            diminishingFactor: 0.85,
            requirements: { threshold: 10, cooldownHours: 168 } // 1 week
        },

        // Engagement milestones
        {
            guildId,
            category: 'engagement',
            milestoneType: 'voice_time_weekly',
            enabled: true,
            rewardAmount: 20,
            maxRewardsPerWeek: 1,
            maxRewardsPerDay: 1,
            diminishingReturns: true,
            diminishingFactor: 0.9,
            requirements: { threshold: 60, cooldownHours: 168 } // 1 hour per week
        }
    ];

    for (const config of defaultConfigs) {
        await MilestoneConfiguration.findOneAndUpdate(
            { guildId: config.guildId, milestoneType: config.milestoneType },
            config,
            { upsert: true, new: true }
        );
    }

    console.log(`[Milestone Service] Created default configurations for guild ${guildId}`);
}

/**
 * Anti-exploitation functions
 */

/**
 * Validates message quality for milestone eligibility
 */
async function validateMessageQuality(
    discordId: string,
    guildId: string,
    messageContent: string,
    channelId: string
): Promise<{ valid: boolean; reason?: string }> {
    try {
        // Check minimum length
        if (messageContent.length < MIN_MESSAGE_LENGTH) {
            return { valid: false, reason: 'Message too short' };
        }

        // Check for spam patterns
        const spamCheck = await detectSpamPatterns(discordId, guildId, messageContent);
        if (!spamCheck.valid) {
            await MilestoneAuditService.logSuspiciousActivity(
                guildId,
                discordId,
                'spam_detection',
                `Spam pattern detected: ${spamCheck.reason}`,
                { messageContent: messageContent.substring(0, 100), channelId }
            );
            return spamCheck;
        }

        // Check for rapid messaging
        const rapidCheck = await detectRapidActivity(discordId, guildId, 'message');
        if (!rapidCheck.valid) {
            await MilestoneAuditService.logSuspiciousActivity(
                guildId,
                discordId,
                'rapid_activity',
                `Rapid messaging detected: ${rapidCheck.reason}`,
                { activityType: 'message', channelId }
            );
            return rapidCheck;
        }

        return { valid: true };
    } catch (error) {
        console.error('[Milestone Service] Error validating message quality:', error);
        return { valid: false, reason: 'Validation error' };
    }
}

/**
 * Validates voice activity for milestone eligibility
 */
async function validateVoiceActivity(
    discordId: string,
    guildId: string,
    channelId: string,
    minutes: number
): Promise<{ valid: boolean; reason?: string }> {
    try {
        // Check minimum session time
        if (minutes < MIN_VOICE_SESSION_MINUTES) {
            return { valid: false, reason: 'Voice session too short' };
        }

        // Check for rapid voice channel switching
        const rapidCheck = await detectRapidActivity(discordId, guildId, 'voice');
        if (!rapidCheck.valid) {
            await MilestoneAuditService.logSuspiciousActivity(
                guildId,
                discordId,
                'rapid_voice_switching',
                `Rapid voice activity detected: ${rapidCheck.reason}`,
                { activityType: 'voice', channelId, minutes }
            );
            return rapidCheck;
        }

        return { valid: true };
    } catch (error) {
        console.error('[Milestone Service] Error validating voice activity:', error);
        return { valid: false, reason: 'Validation error' };
    }
}

/**
 * Detects spam patterns in messages
 */
async function detectSpamPatterns(
    discordId: string,
    guildId: string,
    messageContent: string
): Promise<{ valid: boolean; reason?: string }> {
    try {
        // Check for repeated characters (more than 50% of message)
        const repeatedChars = messageContent.match(/(.)\1{2,}/g);
        if (repeatedChars && repeatedChars.join('').length > messageContent.length * 0.5) {
            return { valid: false, reason: 'Excessive repeated characters' };
        }

        // Check for excessive caps (more than 70% uppercase)
        const upperCaseCount = (messageContent.match(/[A-Z]/g) || []).length;
        const letterCount = (messageContent.match(/[A-Za-z]/g) || []).length;
        if (letterCount > 0 && upperCaseCount / letterCount > 0.7) {
            return { valid: false, reason: 'Excessive uppercase' };
        }

        // Check for duplicate messages in recent history
        const recentMessages = await getRecentUserMessages(discordId, guildId, 5);
        const duplicateCount = recentMessages.filter(msg => msg === messageContent).length;
        if (duplicateCount >= DUPLICATE_MESSAGE_THRESHOLD) {
            return { valid: false, reason: 'Duplicate message spam' };
        }

        return { valid: true };
    } catch (error) {
        console.error('[Milestone Service] Error detecting spam patterns:', error);
        return { valid: true }; // Default to valid on error
    }
}

/**
 * Detects rapid activity patterns
 */
async function detectRapidActivity(
    discordId: string,
    guildId: string,
    activityType: 'message' | 'voice' | 'reaction'
): Promise<{ valid: boolean; reason?: string }> {
    try {
        const now = new Date();
        const oneMinuteAgo = new Date(now.getTime() - (60 * 1000));
        const oneHourAgo = new Date(now.getTime() - (60 * 60 * 1000));

        // Check achievements in the last hour
        const recentAchievements = await MilestoneAchievement.countDocuments({
            discordId,
            guildId,
            timestamp: { $gte: oneHourAgo }
        });

        if (recentAchievements >= MAX_ACHIEVEMENTS_PER_HOUR) {
            return { valid: false, reason: `Too many achievements in last hour: ${recentAchievements}` };
        }

        // Check for rapid activity in the last minute
        const rapidActivityCount = await getRapidActivityCount(discordId, guildId, activityType, oneMinuteAgo);
        if (rapidActivityCount >= RAPID_ACTIVITY_THRESHOLD) {
            return { valid: false, reason: `Rapid ${activityType} activity: ${rapidActivityCount} in 1 minute` };
        }

        return { valid: true };
    } catch (error) {
        console.error('[Milestone Service] Error detecting rapid activity:', error);
        return { valid: true }; // Default to valid on error
    }
}

/**
 * Gets recent user messages for duplicate detection
 */
async function getRecentUserMessages(
    discordId: string,
    guildId: string,
    limit: number
): Promise<string[]> {
    try {
        // This would typically query a message cache or database
        // For now, we'll return an empty array as a placeholder
        // In a real implementation, you'd store recent messages in a cache
        return [];
    } catch (error) {
        console.error('[Milestone Service] Error getting recent messages:', error);
        return [];
    }
}

/**
 * Gets rapid activity count for a user
 */
async function getRapidActivityCount(
    discordId: string,
    guildId: string,
    activityType: string,
    since: Date
): Promise<number> {
    try {
        // Count milestone achievements of this type since the given time
        return await MilestoneAchievement.countDocuments({
            discordId,
            guildId,
            timestamp: { $gte: since },
            details: { $regex: activityType, $options: 'i' }
        });
    } catch (error) {
        console.error('[Milestone Service] Error getting rapid activity count:', error);
        return 0;
    }
}

/**
 * Checks if user is blacklisted or has suspicious activity
 */
async function checkUserSecurity(
    discordId: string,
    guildId: string,
    milestoneType: string
): Promise<{ allowed: boolean; reason?: string }> {
    try {
        const rateLimit = await MilestoneRateLimit.findOne({
            discordId,
            guildId,
            milestoneType
        });

        if (rateLimit) {
            // Check if blacklisted
            if (rateLimit.isBlacklisted) {
                const now = new Date();
                if (rateLimit.blacklistUntil && rateLimit.blacklistUntil > now) {
                    return { allowed: false, reason: 'User is temporarily blacklisted' };
                } else if (rateLimit.blacklistUntil && rateLimit.blacklistUntil <= now) {
                    // Blacklist expired, remove it
                    await MilestoneRateLimit.updateOne(
                        { _id: rateLimit._id },
                        {
                            $unset: { blacklistUntil: 1, blacklistReason: 1 },
                            $set: { isBlacklisted: false }
                        }
                    );

                    await MilestoneAuditService.logBlacklistAction(
                        guildId,
                        discordId,
                        'blacklist_removed',
                        'Blacklist expired automatically'
                    );
                }
            }

            // Check suspicious activity count
            if (rateLimit.suspiciousActivityCount >= SUSPICIOUS_ACTIVITY_THRESHOLD) {
                const lastSuspicious = rateLimit.lastSuspiciousActivity;
                const hoursSinceLastSuspicious = lastSuspicious ?
                    (Date.now() - lastSuspicious.getTime()) / (1000 * 60 * 60) : 24;

                if (hoursSinceLastSuspicious < 24) {
                    // Auto-blacklist for 24 hours
                    const blacklistUntil = new Date(Date.now() + (BLACKLIST_DURATION_HOURS * 60 * 60 * 1000));

                    await MilestoneRateLimit.updateOne(
                        { _id: rateLimit._id },
                        {
                            $set: {
                                isBlacklisted: true,
                                blacklistUntil,
                                blacklistReason: 'Automatic blacklist due to suspicious activity'
                            }
                        }
                    );

                    await MilestoneAuditService.logBlacklistAction(
                        guildId,
                        discordId,
                        'blacklist_added',
                        `Automatic blacklist: ${rateLimit.suspiciousActivityCount} suspicious activities`
                    );

                    return { allowed: false, reason: 'User automatically blacklisted due to suspicious activity' };
                }
            }
        }

        return { allowed: true };
    } catch (error) {
        console.error('[Milestone Service] Error checking user security:', error);
        return { allowed: true }; // Default to allowed on error
    }
}
