import mongoose, { Schema, Document } from 'mongoose';

export interface ITransaction extends Document {
  discordId: string;
  type: 'pay' | 'role_achievement' | 'give' | 'fine' | 'reaction' | 'tax' | 'starter_balance' | 'content_submission' | 'content_reward' | 'milestone';
  amount: number;
  timestamp: Date;
  details?: string;
}

const transactionSchema = new Schema<ITransaction>({
  discordId: { type: String, required: true },
  type: { type: String, enum: ['pay', 'role_achievement', 'give', 'fine', 'reaction', 'tax', 'starter_balance', 'content_submission', 'content_reward', 'milestone'], required: true },
  amount: { type: Number, required: true },
  timestamp: { type: Date, default: Date.now },
  details: { type: String }
});

export default mongoose.model<ITransaction>('Transaction', transactionSchema);
